#!/bin/bash

# Predefined configuration
PREDEFINED_NAMESPACES=("app-dev" "app-stage" "app")
DEFAULT_ROLE="developer-role"  # Default role name in each namespace

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
if ! command_exists kubectl; then
    echo "Error: kubectl is not installed or not in PATH"
    exit 1
fi

if ! command_exists openssl; then
    echo "Error: openssl is not installed"
    exit 1
fi

# Get username
if [ -n "$1" ]; then
    USERNAME="$1"
else
    read -p "Enter username: " USERNAME
fi

# Function to check if user exists
check_user_exists() {
    local username="$1"

    # Check if CSR exists
    if kubectl get csr "${username}-csr" >/dev/null 2>&1; then
        return 0  # User exists
    fi

    # Check if certificate files exist
    if [ -f "user-certs/${username}.crt" ] && [ -f "user-certs/${username}.key" ]; then
        return 0  # User exists
    fi

    return 1  # User doesn't exist
}

# Function to check if user has access to namespaces
check_user_namespace_access() {
    local username="$1"
    local -n namespaces_ref=$2
    local existing_namespaces=()

    for ns in "${namespaces_ref[@]}"; do
        if kubectl get rolebinding -n "$ns" "${username}-access" >/dev/null 2>&1; then
            existing_namespaces+=("$ns")
        fi
    done

    if [ ${#existing_namespaces[@]} -gt 0 ]; then
        echo "User '$username' already has access to namespaces:"
        printf '  - %s\n' "${existing_namespaces[@]}"
        return 0
    fi

    return 1
}

# Function to generate kubeconfig
generate_kubeconfig() {
    local username="$1"
    local -n valid_namespaces_ref=$2

    echo "Generating kubeconfig for user '$username'..."

    # Get cluster information
    SERVER=$(kubectl config view --minify -o jsonpath='{.clusters[0].cluster.server}')
    CLUSTER_NAME=$(kubectl config view --minify -o jsonpath='{.clusters[0].name}')
    CA_DATA=$(kubectl config view --minify -o jsonpath='{.clusters[0].cluster.certificate-authority-data}')
    if [ -z "$CA_DATA" ]; then
        CA_DATA=$(kubectl get secrets -o jsonpath="{.items[?(@.metadata.annotations['kubernetes\.io/service-account\.name']=='default')].data['ca\.crt']}")
    fi

    # Use first namespace as default context
    DEFAULT_NAMESPACE="${valid_namespaces_ref[0]}"

    cat > "user-certs/${username}.kubeconfig" <<EOF
apiVersion: v1
kind: Config
clusters:
- cluster:
    server: ${SERVER}
    certificate-authority-data: ${CA_DATA}
  name: ${CLUSTER_NAME}
contexts:
- context:
    cluster: ${CLUSTER_NAME}
    user: ${username}
    namespace: ${DEFAULT_NAMESPACE}
  name: ${username}-context
current-context: ${username}-context
users:
- name: ${username}
  user:
    client-certificate-data: $(base64 -w0 "user-certs/${username}.crt")
    client-key-data: $(base64 -w0 "user-certs/${username}.key")
EOF
}

# Check if user already exists
USER_EXISTS=false
if check_user_exists "$USERNAME"; then
    USER_EXISTS=true
    echo "User '$USERNAME' already exists in the cluster."
fi

# Display available namespaces
echo -e "\nAvailable namespaces:"
printf '  - %s\n' "${PREDEFINED_NAMESPACES[@]}"

# Get namespace access
read -p $'\nEnter namespaces to grant access to (comma separated): ' INPUT_NAMESPACES
IFS=',' read -ra NAMESPACES <<< "$INPUT_NAMESPACES"

# Validate namespaces
VALID_NAMESPACES=()
for ns in "${NAMESPACES[@]}"; do
    ns=$(echo "$ns" | xargs)  # Trim whitespace

    # Check if namespace is in predefined list
    if [[ ! " ${PREDEFINED_NAMESPACES[*]} " =~ " ${ns} " ]]; then
        echo "Warning: Skipping invalid namespace '$ns' - not in predefined list"
        continue
    fi

    # Check if namespace exists in cluster
    if ! kubectl get namespace "$ns" >/dev/null 2>&1; then
        echo "Warning: Namespace '$ns' does not exist in cluster"
        continue
    fi

    # Check if default role exists
    if ! kubectl get role -n "$ns" "$DEFAULT_ROLE" >/dev/null 2>&1; then
        echo "Warning: Default role '$DEFAULT_ROLE' not found in namespace '$ns'"
        continue
    fi

    VALID_NAMESPACES+=("$ns")
done

if [ ${#VALID_NAMESPACES[@]} -eq 0 ]; then
    echo "Error: No valid namespaces selected"
    exit 1
fi

echo -e "\nGranting access to namespaces:"
printf '  - %s\n' "${VALID_NAMESPACES[@]}"

# Create certs directory
mkdir -p user-certs

# Handle existing user vs new user
if [ "$USER_EXISTS" = true ]; then
    echo -e "\n=== Processing Existing User ==="

    # Check if user already has access to requested namespaces
    if check_user_namespace_access "$USERNAME" VALID_NAMESPACES; then
        echo -e "\nUser already has access to some requested namespaces."
        echo "Proceeding to generate kubeconfig for existing user..."
    else
        echo -e "\nUser exists but doesn't have access to requested namespaces."
        echo "Creating additional RoleBindings..."

        # Create RoleBindings for namespaces user doesn't have access to
        for ns in "${VALID_NAMESPACES[@]}"; do
            if ! kubectl get rolebinding -n "$ns" "${USERNAME}-access" >/dev/null 2>&1; then
                echo "Creating RoleBinding in namespace: $ns"
                cat <<EOF | kubectl apply -f -
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: ${USERNAME}-access
  namespace: ${ns}
subjects:
- kind: User
  name: ${USERNAME}
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: Role
  name: ${DEFAULT_ROLE}
  apiGroup: rbac.authorization.k8s.io
EOF
            else
                echo "RoleBinding already exists in namespace: $ns"
            fi
        done
    fi

    # Verify certificates exist for existing user
    if [ ! -f "user-certs/${USERNAME}.crt" ] || [ ! -f "user-certs/${USERNAME}.key" ]; then
        echo "Warning: Certificate files not found locally. Attempting to retrieve from cluster..."

        # Try to get certificate from existing CSR
        if kubectl get csr "${USERNAME}-csr" -o jsonpath='{.status.certificate}' &>/dev/null; then
            kubectl get csr "${USERNAME}-csr" -o jsonpath='{.status.certificate}' | base64 -d > "user-certs/${USERNAME}.crt"
            echo "Retrieved certificate from cluster CSR."
        else
            echo "Error: Cannot retrieve certificate for existing user. Certificate files missing and CSR not found."
            exit 1
        fi

        # Note: Private key cannot be retrieved from cluster for security reasons
        if [ ! -f "user-certs/${USERNAME}.key" ]; then
            echo "Error: Private key file 'user-certs/${USERNAME}.key' not found."
            echo "Private keys cannot be retrieved from the cluster for security reasons."
            echo "Please ensure the private key file exists or recreate the user."
            exit 1
        fi
    fi

    # Generate kubeconfig for existing user
    generate_kubeconfig "$USERNAME" VALID_NAMESPACES
    echo -e "\n✓ Kubeconfig generated for existing user '$USERNAME'"

else
    echo -e "\n=== Creating New User ==="

    # Generate private key
    openssl genrsa -out "user-certs/${USERNAME}.key" 2048

    # Create CSR config
    cat > "user-certs/${USERNAME}.csr.conf" <<EOF
[ req ]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
[ dn ]
CN = ${USERNAME}
O = dev-group
[ v3_ext ]
authorityKeyIdentifier=keyid,issuer:always
basicConstraints=CA:FALSE
keyUsage=keyEncipherment,dataEncipherment
extendedKeyUsage=serverAuth,clientAuth
EOF

    # Generate CSR
    openssl req -new -key "user-certs/${USERNAME}.key" \
        -out "user-certs/${USERNAME}.csr" \
        -config "user-certs/${USERNAME}.csr.conf"

    # Create Kubernetes CSR
    cat <<EOF | kubectl apply -f -
apiVersion: certificates.k8s.io/v1
kind: CertificateSigningRequest
metadata:
  name: ${USERNAME}-csr
spec:
  groups:
  - system:authenticated
  request: $(base64 -w0 < "user-certs/${USERNAME}.csr")
  signerName: kubernetes.io/kube-apiserver-client
  usages:
  - client auth
EOF

    # Approve CSR
    kubectl certificate approve "${USERNAME}-csr"

    # Wait for certificate issuance
    echo -e "\nWaiting for certificate issuance..."
    for _ in {1..10}; do
        if kubectl get csr "${USERNAME}-csr" -o jsonpath='{.status.certificate}' &>/dev/null; then
            break
        fi
        sleep 1
    done

    # Retrieve certificate
    kubectl get csr "${USERNAME}-csr" -o jsonpath='{.status.certificate}' | base64 -d > "user-certs/${USERNAME}.crt"

    # Verify certificate exists
    if [ ! -s "user-certs/${USERNAME}.crt" ]; then
        echo "Error: Failed to get signed certificate for user"
        exit 1
    fi

    # Create RoleBindings for each namespace
    for ns in "${VALID_NAMESPACES[@]}"; do
        echo "Creating RoleBinding in namespace: $ns"

        # Create RoleBinding
        cat <<EOF | kubectl apply -f -
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: ${USERNAME}-access
  namespace: ${ns}
subjects:
- kind: User
  name: ${USERNAME}
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: Role
  name: ${DEFAULT_ROLE}
  apiGroup: rbac.authorization.k8s.io
EOF
    done

    # Generate kubeconfig for new user
    generate_kubeconfig "$USERNAME" VALID_NAMESPACES
    echo -e "\n✓ New user '$USERNAME' created successfully"
fi


# Generate kubeconfig
SERVER=$(kubectl config view --minify -o jsonpath='{.clusters[0].cluster.server}')
CLUSTER_NAME=$(kubectl config view --minify -o jsonpath='{.clusters[0].name}')
CA_DATA=$(kubectl config view --minify -o jsonpath='{.clusters[0].cluster.certificate-authority-data}')
if [ -z "$CA_DATA" ]; then
    CA_DATA=$(kubectl get secrets -o jsonpath="{.items[?(@.metadata.annotations['kubernetes\.io/service-account\.name']=='default')}.data['ca\.crt']}")
fi

# Use first namespace as default context
DEFAULT_NAMESPACE="${VALID_NAMESPACES[0]}"

cat > "user-certs/${USERNAME}.kubeconfig" <<EOF
apiVersion: v1
kind: Config
clusters:
- cluster:
    server: ${SERVER}
    certificate-authority-data: ${CA_DATA}
  name: ${CLUSTER_NAME}
contexts:
- context:
    cluster: ${CLUSTER_NAME}
    user: ${USERNAME}
    namespace: ${DEFAULT_NAMESPACE}
  name: ${USERNAME}-context
current-context: ${USERNAME}-context
users:
- name: ${USERNAME}
  user:
    client-certificate-data: $(base64 -w0 "user-certs/${USERNAME}.crt")
    client-key-data: $(base64 -w0 "user-certs/${USERNAME}.key")
EOF

# Verification function
verify_access() {
    echo -e "\n=== Verification Results ==="

    # 1. Verify CSR
    echo -e "\n[1] Certificate Signing Request:"
    kubectl get csr "${USERNAME}-csr" -o wide

    # 2. Verify RoleBindings
    echo -e "\n[2] RoleBindings:"
    for ns in "${VALID_NAMESPACES[@]}"; do
        echo -e "\nNamespace: $ns"
        kubectl get rolebinding -n "$ns" "${USERNAME}-access" -o wide
        echo "Associated Role:"
        kubectl describe rolebinding -n "$ns" "${USERNAME}-access" | grep -A5 "^Role:"
    done

    # 3. Test access with generated kubeconfig
    echo -e "\n[3] Access Test:"
    for ns in "${VALID_NAMESPACES[@]}"; do
        echo -n "  - Access to $ns: "
        if kubectl --kubeconfig="user-certs/${USERNAME}.kubeconfig" auth can-i get pods -n "$ns" 2>/dev/null; then
            echo "Success"
        else
            echo "Failed"
        fi
    done
}

# Perform verification
verify_access

echo -e "\nUser setup completed!"
echo "Kubeconfig file: user-certs/${USERNAME}.kubeconfig"
echo "To test access:"
echo "  kubectl --kubeconfig=user-certs/${USERNAME}.kubeconfig get pods"